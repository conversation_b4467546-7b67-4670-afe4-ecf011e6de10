import fitz  # PyMuPDF
import os

def split_double_layer_pdf(input_pdf_path, output_text_pdf=None, output_image_pdf=None):
    """
    将双层PDF分离成文本PDF和图像PDF
    
    Args:
        input_pdf_path: PDFtest.pdf
        output_text_pdf: 输出文本PDF路径（可选）
        output_image_pdf: 输出图像PDF路径（可选）
    """
    
    if not os.path.exists(input_pdf_path):
        print(f"错误: 找不到文件 {input_pdf_path}")
        return False
    
    # 设置默认输出文件名
    base_name = os.path.splitext(input_pdf_path)[0]
    if output_text_pdf is None:
        output_text_pdf = f"{base_name}_text_only.pdf"
    if output_image_pdf is None:
        output_image_pdf = f"{base_name}_images_only.pdf"
    
    try:
        # 打开原始PDF
        pdf_doc = fitz.open(input_pdf_path)
        
        # 创建文本PDF
        create_text_pdf(pdf_doc, output_text_pdf)
        
        # 创建图像PDF
        create_image_pdf(pdf_doc, output_image_pdf)
        
        pdf_doc.close()
        
        print(f"✅ 分离完成!")
        print(f"📄 文本PDF: {output_text_pdf}")
        print(f"🖼️  图像PDF: {output_image_pdf}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理PDF时出错: {str(e)}")
        return False

def create_text_pdf(pdf_doc, output_path):
    """创建纯文本PDF"""
    print("正在创建文本PDF...")
    
    # 创建新的PDF文档
    text_doc = fitz.open()
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc.load_page(page_num)
        
        # 提取文本内容
        text_content = page.get_text()
        
        if text_content.strip():  # 如果页面有文本内容
            # 获取页面尺寸
            rect = page.rect
            
            # 创建新页面
            new_page = text_doc.new_page(width=rect.width, height=rect.height)
            
            # 获取文本块信息（包含位置和格式）
            text_dict = page.get_text("dict")
            
            for block in text_dict["blocks"]:
                if "lines" in block:  # 文本块
                    for line in block["lines"]:
                        for span in line["spans"]:
                            # 获取文本和位置信息
                            text = span["text"]
                            bbox = span["bbox"]
                            font_size = span["size"]
                            font_name = span["font"]
                            
                            # 在新页面上插入文本
                            point = fitz.Point(bbox[0], bbox[1])
                            try:
                                new_page.insert_text(
                                    point,
                                    text,
                                    fontsize=font_size,
                                    fontname="helv"  # 使用默认字体
                                )
                            except:
                                # 如果字体有问题，使用默认字体
                                new_page.insert_text(
                                    point,
                                    text,
                                    fontsize=font_size
                                )
        else:
            # 如果没有文本，创建空白页面
            rect = page.rect
            new_page = text_doc.new_page(width=rect.width, height=rect.height)
            # 添加页面标记
            new_page.insert_text(
                fitz.Point(50, 50), 
                f"第{page_num + 1}页 - 无文本内容", 
                fontsize=12
            )
    
    # 保存文本PDF
    text_doc.save(output_path)
    text_doc.close()

def create_image_pdf(pdf_doc, output_path):
    """创建纯图像PDF"""
    print("正在创建图像PDF...")
    
    # 创建新的PDF文档
    image_doc = fitz.open()
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc.load_page(page_num)
        
        # 获取页面尺寸
        rect = page.rect
        
        # 创建新页面
        new_page = image_doc.new_page(width=rect.width, height=rect.height)
        
        # 获取页面上的所有图像
        image_list = page.get_images()
        
        if image_list:
            for img_index, img in enumerate(image_list):
                try:
                    # 获取图像数据
                    xref = img[0]
                    pix = fitz.Pixmap(pdf_doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # 确保不是CMYK
                        # 获取图像在页面上的位置
                        img_rect = page.get_image_bbox(img)
                        
                        # 将图像插入到新页面
                        img_data = pix.tobytes("png")
                        new_page.insert_image(img_rect, stream=img_data)
                    
                    pix = None  # 释放内存
                    
                except Exception as e:
                    print(f"处理第{page_num + 1}页的图像{img_index + 1}时出错: {e}")
        
        # 复制绘图元素（矢量图形）
        drawings = page.get_drawings()
        if drawings:
            for drawing in drawings:
                try:
                    # 重新绘制矢量图形
                    for item in drawing["items"]:
                        if item[0] == "l":  # 线条
                            new_page.draw_line(item[1], item[2])
                        elif item[0] == "re":  # 矩形
                            new_page.draw_rect(fitz.Rect(item[1]))
                        elif item[0] == "c":  # 曲线
                            new_page.draw_bezier(item[1], item[2], item[3], item[4])
                except Exception as e:
                    print(f"处理第{page_num + 1}页的绘图元素时出错: {e}")
        
        # 如果页面没有图像和绘图，添加标记
        if not image_list and not drawings:
            new_page.insert_text(
                fitz.Point(50, 50), 
                f"第{page_num + 1}页 - 无图像内容", 
                fontsize=12
            )
    
    # 保存图像PDF
    image_doc.save(output_path)
    image_doc.close()

def main():
    input_pdf = "PDFtest.pdf"
    
    if not os.path.exists(input_pdf):
        print(f"❌ 错误: 找不到文件 {input_pdf}")
        print("请确保PDFtest.pdf文件在当前目录中")
        return
    
    print("🔄 开始分离双层PDF...")
    print(f"📁 输入文件: {input_pdf}")
    
    success = split_double_layer_pdf(
        input_pdf,
        "PDFtest_text_only.pdf",
        "PDFtest_images_only.pdf"
    )
    
    if success:
        print("\n📊 分离统计:")
        # 显示原文件信息
        original_doc = fitz.open(input_pdf)
        print(f"原文件页数: {original_doc.page_count}")
        original_doc.close()
        
        # 显示分离后文件信息
        if os.path.exists("PDFtest_text_only.pdf"):
            text_doc = fitz.open("PDFtest_text_only.pdf")
            print(f"文本PDF页数: {text_doc.page_count}")
            text_doc.close()
        
        if os.path.exists("PDFtest_images_only.pdf"):
            image_doc = fitz.open("PDFtest_images_only.pdf")
            print(f"图像PDF页数: {image_doc.page_count}")
            image_doc.close()

if __name__ == "__main__":
    main()
