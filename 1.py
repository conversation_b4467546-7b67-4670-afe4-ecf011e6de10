import fitz  # PyMuPDF
from paddleocr import PaddleOCR
import os

# 初始化 PaddleOCR，设置为中文
ocr = PaddleOCR(use_angle_cls=True, lang="ch")  # 'ch' 表示中文，英文用 'en'

# 设置输入和输出文件夹
input_dir = "C:/Users/<USER>/Desktop/pdfocr/input"  # 替换为你的 PDF 文件夹路径
output_dir = "C:/Users/<USER>/Desktop/pdfocr/output"  # 替换为输出文本文件夹路径
os.makedirs(output_dir, exist_ok=True)

# 处理单个 PDF 的函数
def process_pdf(pdf_file):
    input_path = os.path.join(input_dir, pdf_file)
    output_path = os.path.join(output_dir, f"{pdf_file}.txt")
    output_text = ""

    try:
        # 打开 PDF
        pdf = fitz.open(input_path)
        for page_num in range(pdf.page_count):
            page = pdf.load_page(page_num)
            # 将页面渲染为图像（300 DPI）
            pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))
            img_data = pix.tobytes("png")  # 转为 PNG 格式

            # 使用 PaddleOCR 进行文字识别
            result = ocr.ocr(img_data, cls=True)
            text = "\n".join([line[1][0] for line in result[0]])  # 提取文字
            output_text += f"第 {page_num + 1} 页:\n{text}\n\n"

        # 保存提取的文字到文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(output_text)
        print(f"处理完成: {pdf_file}")
    except Exception as e:
        print(f"处理 {pdf_file} 时出错: {str(e)}")
    finally:
        pdf.close()

# 批量处理文件夹中的所有 PDF
pdf_files = [f for f in os.listdir(input_dir) if f.lower().endswith(".pdf")]
for pdf_file in pdf_files:
    process_pdf(pdf_file)