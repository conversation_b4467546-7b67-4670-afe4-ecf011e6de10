import fitz  # PyMuPDF
import os
import sys

def detect_pdf_type(pdf_path):
    """
    检测PDF文件的类型
    返回字典包含以下信息：
    - is_text_based: 是否是纯文本PDF
    - has_images: 是否包含图像
    - is_searchable: 是否可搜索（有文本层）
    - layer_type: 单层(single)或双层(double)
    - page_analysis: 每页的详细分析
    """
    
    if not os.path.exists(pdf_path):
        return {"error": f"文件不存在: {pdf_path}"}
    
    try:
        pdf_doc = fitz.open(pdf_path)
        total_pages = pdf_doc.page_count
        
        result = {
            "file_path": pdf_path,
            "total_pages": total_pages,
            "is_text_based": True,
            "has_images": False,
            "is_searchable": True,
            "layer_type": "single",
            "page_analysis": []
        }
        
        text_pages = 0
        image_pages = 0
        mixed_pages = 0
        
        for page_num in range(total_pages):
            page = pdf_doc.load_page(page_num)
            
            # 提取文本内容
            text_content = page.get_text().strip()
            
            # 获取图像列表
            image_list = page.get_images()
            
            # 获取绘图指令（用于检测矢量图形）
            drawings = page.get_drawings()
            
            page_info = {
                "page_number": page_num + 1,
                "has_text": bool(text_content),
                "text_length": len(text_content),
                "has_images": len(image_list) > 0,
                "image_count": len(image_list),
                "has_drawings": len(drawings) > 0,
                "drawing_count": len(drawings)
            }
            
            # 分析页面类型
            if text_content and len(image_list) == 0:
                page_info["page_type"] = "纯文本"
                text_pages += 1
            elif len(image_list) > 0 and not text_content:
                page_info["page_type"] = "纯图像"
                image_pages += 1
                result["is_text_based"] = False
            elif len(image_list) > 0 and text_content:
                page_info["page_type"] = "图文混合"
                mixed_pages += 1
                result["has_images"] = True
            else:
                page_info["page_type"] = "空白或其他"
            
            result["page_analysis"].append(page_info)
        
        # 判断整体文档类型
        if image_pages > 0 or mixed_pages > 0:
            result["has_images"] = True
        
        if text_pages == 0 and image_pages > 0:
            result["is_text_based"] = False
            result["is_searchable"] = False
            result["layer_type"] = "single"
        elif mixed_pages > 0:
            # 有图像又有文本，可能是双层PDF
            result["layer_type"] = "double"
            result["is_text_based"] = False
        elif text_pages == total_pages:
            # 全部是纯文本页面
            result["is_text_based"] = True
            result["layer_type"] = "single"
        
        pdf_doc.close()
        return result
        
    except Exception as e:
        return {"error": f"处理PDF时出错: {str(e)}"}

def print_analysis_result(result):
    """打印分析结果"""
    if "error" in result:
        print(f"错误: {result['error']}")
        return
    
    print("=" * 60)
    print(f"PDF文件分析结果: {result['file_path']}")
    print("=" * 60)
    print(f"总页数: {result['total_pages']}")
    print(f"是否为纯文本文档: {'是' if result['is_text_based'] else '否'}")
    print(f"是否包含图像: {'是' if result['has_images'] else '否'}")
    print(f"是否可搜索: {'是' if result['is_searchable'] else '否'}")
    print(f"文档层次类型: {'双层PDF' if result['layer_type'] == 'double' else '单层PDF'}")
    
    print("\n页面详细分析:")
    print("-" * 60)
    
    for page_info in result['page_analysis']:
        print(f"第{page_info['page_number']}页: {page_info['page_type']}")
        print(f"  - 文本长度: {page_info['text_length']} 字符")
        print(f"  - 图像数量: {page_info['image_count']}")
        print(f"  - 绘图元素: {page_info['drawing_count']}")
    
    print("\n总结:")
    print("-" * 60)
    
    if result['is_text_based']:
        print("✓ 这是一个纯文本PDF文档")
    elif result['layer_type'] == 'double':
        print("✓ 这是一个双层PDF文档（包含图像和可搜索的文本层）")
    elif result['has_images'] and not result['is_searchable']:
        print("✓ 这是一个图像型PDF文档（需要OCR才能提取文本）")
    else:
        print("✓ 这是一个混合型PDF文档")

def main():
    # 检测PDFtest.pdf文件
    pdf_file = "PDFtest.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: 找不到文件 {pdf_file}")
        print("请确保PDFtest.pdf文件在当前目录中")
        return
    
    print("正在分析PDF文件...")
    result = detect_pdf_type(pdf_file)
    print_analysis_result(result)

if __name__ == "__main__":
    main()
